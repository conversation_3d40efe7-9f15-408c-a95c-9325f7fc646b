// Package ai internal/modules/ai/manager.go
package ai

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"

	"github.com/koopa0/assistant-go/internal/config"
	"github.com/koopa0/assistant-go/internal/modules"
	"github.com/koopa0/assistant-go/internal/modules/ai/providers"
	"github.com/koopa0/assistant-go/internal/utils"
)

// Manager AI 助手管理器
type Manager struct {
	config       *config.AIConfig
	providers    map[string]Provider
	conversation []ConversationMessage
	mutex        sync.RWMutex
	logger       *utils.Logger

	// UI 元件
	ui *UI

	// 資料綁定
	currentMessage binding.String
	isProcessing   binding.Bool

	// 開發助手功能
	codeAnalyzer  *CodeAnalyzer
	projectHelper *ProjectHelper
}

// ConversationMessage 對話訊息
type ConversationMessage struct {
	Role      string // "user", "assistant", "system"
	Content   string
	Timestamp time.Time
	Provider  string
	Metadata  map[string]interface{}
}

// NewManager 建立新的 AI 助手管理器
func NewManager(cfg *config.AIConfig, logger *utils.Logger) *Manager {
	return &Manager{
		config:         cfg,
		providers:      make(map[string]Provider),
		conversation:   []ConversationMessage{},
		currentMessage: binding.NewString(),
		isProcessing:   binding.NewBool(),
		logger:         logger,
	}
}

// NewAssistant 建立新的 AI 助手 (為了兼容性)
func NewAssistant(cfg *config.AIConfig, logger *utils.Logger) *Manager {
	return NewManager(cfg, logger)
}

// Initialize 實作 Module 介面
func (m *Manager) Initialize(ctx context.Context) error {
	m.logger.Info("Initializing AI module...")

	// 初始化 AI 提供者
	if err := m.initializeProviders(); err != nil {
		return utils.WrapError(err, utils.ErrCodeModule, "failed to initialize AI providers")
	}

	// 初始化開發助手功能
	m.codeAnalyzer = NewCodeAnalyzer()
	m.projectHelper = NewProjectHelper()

	// 建立 UI
	m.ui = NewUI(m)

	// 添加歡迎訊息
	m.addWelcomeMessage()

	m.logger.Info("AI module initialized successfully")
	return nil
}

// Content 實作 Module 介面
func (m *Manager) Content() fyne.CanvasObject {
	if m.ui == nil {
		return container.NewVBox()
	}
	return m.ui.Content()
}

// Refresh 實作 Module 介面
func (m *Manager) Refresh() error {
	m.logger.Info("Refreshing AI module...")

	if m.ui != nil {
		m.ui.Refresh()
	}

	return nil
}

// Shutdown 實作 Module 介面
func (m *Manager) Shutdown() error {
	m.logger.Info("Shutting down AI module...")

	// 清理資源
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for name, provider := range m.providers {
		if err := provider.Shutdown(); err != nil {
			m.logger.Warn("Failed to shutdown AI provider %s: %v", name, err)
		}
	}

	m.logger.Info("AI module shut down successfully")
	return nil
}

// Info 實作 Module 介面
func (m *Manager) Info() modules.ModuleInfo {
	return modules.ModuleInfo{
		ID:          "ai",
		Name:        "AI Brain",
		Description: "AI assistant with Claude, Gemini, and OpenAI integration",
		Version:     "1.0.0",
		Author:      "Koopa",
	}
}

// initializeProviders 初始化 AI 提供者
func (m *Manager) initializeProviders() error {
	// 暫時使用模擬配置，直到配置結構修正
	// Claude AI
	claudeConfig := &providers.ClaudeConfig{
		APIKey: "demo-key",
		Model:  "claude-3-sonnet-20240229",
	}
	provider := providers.NewClaudeProvider(claudeConfig, m.logger)
	m.providers["claude"] = provider
	m.logger.Info("Claude AI provider initialized")

	// Gemini AI
	geminiConfig := &providers.GeminiConfig{
		APIKey: "demo-key",
		Model:  "gemini-pro",
	}
	geminiProvider := providers.NewGeminiProvider(geminiConfig, m.logger)
	m.providers["gemini"] = geminiProvider
	m.logger.Info("Gemini AI provider initialized")

	// OpenAI (備用)
	openaiConfig := &providers.OpenAIConfig{
		APIKey: "demo-key",
		Model:  "gpt-4",
	}
	openaiProvider := providers.NewOpenAIProvider(openaiConfig, m.logger)
	m.providers["openai"] = openaiProvider
	m.logger.Info("OpenAI provider initialized")

	return nil
}

// SendMessage 發送訊息給 AI
func (m *Manager) SendMessage(message string) error {
	if strings.TrimSpace(message) == "" {
		return fmt.Errorf("message cannot be empty")
	}

	// 添加用戶訊息到對話
	m.addUserMessage(message)

	// 設定處理狀態
	m.isProcessing.Set(true)

	// 在背景處理 AI 回應
	go m.processAIResponse(message)

	return nil
}

// processAIResponse 處理 AI 回應
func (m *Manager) processAIResponse(message string) {
	defer func() {
		m.isProcessing.Set(false)
	}()

	provider := m.providers[m.config.DefaultProvider]
	if provider == nil {
		m.addAssistantMessage("錯誤: 未找到可用的 AI 提供者", "error")
		return
	}

	// 調用 AI 提供者
	ctx := context.Background()
	response, err := provider.SendMessage(ctx, message)
	if err != nil {
		m.addAssistantMessage(fmt.Sprintf("錯誤: %v", err), "error")
		return
	}

	m.addAssistantMessage(response.Content, response.Provider)
}

// addUserMessage 添加用戶訊息
func (m *Manager) addUserMessage(content string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	msg := ConversationMessage{
		Role:      "user",
		Content:   content,
		Timestamp: time.Now(),
		Provider:  m.config.DefaultProvider,
	}
	m.conversation = append(m.conversation, msg)

	if m.ui != nil {
		m.ui.RefreshChat()
	}
}

// addAssistantMessage 添加助手訊息
func (m *Manager) addAssistantMessage(content, provider string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	msg := ConversationMessage{
		Role:      "assistant",
		Content:   content,
		Timestamp: time.Now(),
		Provider:  provider,
	}
	m.conversation = append(m.conversation, msg)

	if m.ui != nil {
		m.ui.RefreshChat()
	}
}

// addSystemMessage 添加系統訊息
func (m *Manager) addSystemMessage(content string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	msg := ConversationMessage{
		Role:      "system",
		Content:   content,
		Timestamp: time.Now(),
		Provider:  "system",
	}
	m.conversation = append(m.conversation, msg)

	if m.ui != nil {
		m.ui.RefreshChat()
	}
}

// addWelcomeMessage 添加歡迎訊息
func (m *Manager) addWelcomeMessage() {
	welcome := `# 歡迎使用 Koopa 的 AI 開發助手！

我是您的個人 AI 助手，專門為開發工作而設計。我可以幫助您：

## 🔧 開發協助
- **程式碼分析與審查**
- **生成單元測試**
- **重構建議**
- **效能優化建議**

## 🚀 專案管理
- **專案架構建議**
- **技術選型協助**
- **最佳實踐指導**

## 🐛 除錯協助
- **錯誤分析**
- **日誌解讀**
- **問題排查**

## 💡 其他功能
- **技術文件撰寫**
- **API 設計建議**
- **資料庫優化**

請隨時向我提問，或使用快速動作按鈕開始！`

	m.addAssistantMessage(welcome, "system")
}

// GetConversation 取得對話記錄
func (m *Manager) GetConversation() []ConversationMessage {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 返回副本以避免併發問題
	conversation := make([]ConversationMessage, len(m.conversation))
	copy(conversation, m.conversation)
	return conversation
}

// ClearConversation 清空對話
func (m *Manager) ClearConversation() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.conversation = []ConversationMessage{}
	if m.ui != nil {
		m.ui.RefreshChat()
	}
	m.addWelcomeMessage()
}

// GetAvailableProviders 取得可用的 AI 提供者
func (m *Manager) GetAvailableProviders() []string {
	providers := make([]string, 0, len(m.providers))
	for name := range m.providers {
		providers = append(providers, name)
	}
	return providers
}
