// Package ai internal/modules/ai/ui.go
package ai

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
)

// UI AI 模組的使用者介面
type UI struct {
	manager *Manager
	content fyne.CanvasObject

	// UI 元件
	chatHistory    *widget.List
	messageInput   *widget.Entry
	sendButton     *widget.Button
	providerSelect *widget.Select
	modelSelect    *widget.Select
	settingsPanel  *fyne.Container
	contextPanel   *fyne.Container
}

// NewUI 建立新的 AI UI
func NewUI(manager *Manager) *UI {
	ui := &UI{
		manager: manager,
	}
	ui.buildUI()
	return ui
}

// Content 返回 UI 內容
func (ui *UI) Content() fyne.CanvasObject {
	return ui.content
}

// buildUI 建立使用者介面
func (ui *UI) buildUI() {
	// 建立聊天歷史
	ui.createChatHistory()

	// 建立輸入區域
	inputArea := ui.createInputArea()

	// 建立設定面板
	ui.createSettingsPanel()

	// 建立上下文面板
	ui.createContextPanel()

	// 建立工具列
	toolbar := ui.createToolbar()

	// 主要聊天區域 - 添加滾動支援
	chatArea := container.NewBorder(
		toolbar,
		inputArea,
		nil,
		nil,
		container.NewScroll(ui.chatHistory), // 聊天歷史添加滾動
	)

	// 側邊面板 - 添加滾動支援
	sidePanel := container.NewAppTabs(
		container.NewTabItem("設定", container.NewScroll(ui.settingsPanel)),               // 設定面板添加滾動
		container.NewTabItem("上下文", container.NewScroll(ui.contextPanel)),               // 上下文面板添加滾動
		container.NewTabItem("開發助手", container.NewScroll(ui.createDevAssistantPanel())), // 開發助手面板添加滾動
	)

	// 組合主要佈局
	ui.content = container.NewHSplit(
		container.NewScroll(chatArea),  // 聊天區域添加滾動
		container.NewScroll(sidePanel), // 側邊面板添加滾動
	)
}

// createChatHistory 建立聊天歷史
func (ui *UI) createChatHistory() {
	ui.chatHistory = widget.NewList(
		func() int {
			return len(ui.manager.GetConversation())
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				container.NewHBox(
					widget.NewIcon(theme.AccountIcon()),
					widget.NewLabelWithStyle("角色", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
					widget.NewLabel("時間"),
				),
				widget.NewRichTextFromMarkdown("訊息內容"),
			)
		},
		func(id widget.ListItemID, item fyne.CanvasObject) {
			conversation := ui.manager.GetConversation()
			if id >= len(conversation) {
				return
			}

			msg := conversation[id]
			container := item.(*fyne.Container)

			// 更新標題列
			header := container.Objects[0].(*fyne.Container)
			icon := header.Objects[0].(*widget.Icon)
			roleLabel := header.Objects[1].(*widget.Label)
			timeLabel := header.Objects[2].(*widget.Label)

			// 根據角色設定圖示和顏色
			switch msg.Role {
			case "user":
				icon.SetResource(theme.AccountIcon())
				roleLabel.SetText("您")
			case "assistant":
				icon.SetResource(theme.ComputerIcon())
				roleLabel.SetText("AI 助手")
			case "system":
				icon.SetResource(theme.InfoIcon())
				roleLabel.SetText("系統")
			}

			timeLabel.SetText(msg.Timestamp.Format("15:04"))

			// 更新訊息內容
			content := container.Objects[1].(*widget.RichText)
			content.ParseMarkdown(msg.Content)
		},
	)
}

// createInputArea 建立輸入區域
func (ui *UI) createInputArea() *fyne.Container {
	// 訊息輸入框
	ui.messageInput = widget.NewMultiLineEntry()
	ui.messageInput.Bind(ui.manager.currentMessage)
	ui.messageInput.SetPlaceHolder("輸入您的問題或請求...")
	ui.messageInput.Wrapping = fyne.TextWrapWord

	// 發送按鈕
	ui.sendButton = widget.NewButtonWithIcon("發送", theme.MailSendIcon(), ui.sendMessage)

	// 快速動作按鈕
	quickActions := container.NewHBox(
		widget.NewButtonWithIcon("程式碼分析", theme.DocumentIcon(), ui.analyzeCode),
		widget.NewButtonWithIcon("專案協助", theme.FolderIcon(), ui.projectAssist),
		widget.NewButtonWithIcon("除錯協助", theme.BrokenImageIcon(), ui.debugAssist),
		widget.NewButtonWithIcon("清空對話", theme.DeleteIcon(), ui.clearConversation),
	)

	// 組合輸入區域
	return container.NewBorder(
		quickActions,
		container.NewHBox(
			widget.NewLabel(""),
			ui.sendButton,
		),
		nil,
		nil,
		container.NewScroll(ui.messageInput),
	)
}

// createSettingsPanel 建立設定面板
func (ui *UI) createSettingsPanel() {
	// 提供者選擇
	providers := ui.manager.GetAvailableProviders()
	ui.providerSelect = widget.NewSelect(providers, ui.onProviderChanged)
	ui.providerSelect.SetSelected(ui.manager.config.DefaultProvider)

	// 模型選擇
	ui.modelSelect = widget.NewSelect([]string{}, ui.onModelChanged)
	ui.updateModelList()

	// 設定項目
	ui.settingsPanel = container.NewVBox(
		widget.NewLabelWithStyle("AI 設定", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		widget.NewSeparator(),

		widget.NewLabel("AI 提供者:"),
		ui.providerSelect,

		widget.NewLabel("模型:"),
		ui.modelSelect,

		widget.NewLabel("系統提示:"),
		widget.NewMultiLineEntry(), // TODO: 綁定到系統提示

		widget.NewSeparator(),
		widget.NewButton("儲存設定", ui.saveSettings),
		widget.NewButton("重設設定", ui.resetSettings),
	)
}

// createContextPanel 建立上下文面板
func (ui *UI) createContextPanel() {
	projectInfo := ui.manager.projectHelper.GetProjectInfo()
	gitStatus := ui.manager.projectHelper.GetGitStatus()

	ui.contextPanel = container.NewVBox(
		widget.NewLabelWithStyle("對話上下文", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		widget.NewSeparator(),
		widget.NewLabel("專案資訊:"),
		widget.NewLabel(projectInfo),
		widget.NewLabel("Git 狀態:"),
		widget.NewLabel(gitStatus),
		widget.NewSeparator(),
		widget.NewButton("重新載入上下文", ui.refreshContext),
	)
}

// createDevAssistantPanel 建立開發助手面板
func (ui *UI) createDevAssistantPanel() *fyne.Container {
	return container.NewVBox(
		widget.NewLabelWithStyle("開發助手工具", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		widget.NewSeparator(),

		widget.NewButton("程式碼審查", func() {
			ui.manager.addSystemMessage("開始程式碼審查...")
		}),
		widget.NewButton("生成測試", func() {
			ui.manager.addSystemMessage("生成單元測試...")
		}),
		widget.NewButton("重構建議", func() {
			ui.manager.addSystemMessage("分析重構機會...")
		}),
		widget.NewButton("效能優化", func() {
			ui.manager.addSystemMessage("分析效能瓶頸...")
		}),

		widget.NewSeparator(),
		widget.NewLabel("AI 整合狀態:"),
		widget.NewLabel("✓ 模組已載入"),
		widget.NewLabel("✓ 提供者已配置"),
	)
}

// createToolbar 建立工具列
func (ui *UI) createToolbar() *widget.Toolbar {
	return widget.NewToolbar(
		widget.NewToolbarAction(theme.DocumentCreateIcon(), func() {
			ui.manager.addSystemMessage("開始新對話...")
		}),
		widget.NewToolbarSeparator(),
		widget.NewToolbarAction(theme.DocumentSaveIcon(), func() {
			ui.saveConversation()
		}),
		widget.NewToolbarAction(theme.FolderOpenIcon(), func() {
			ui.loadConversation()
		}),
		widget.NewToolbarSeparator(),
		widget.NewToolbarAction(theme.SettingsIcon(), func() {
			ui.showAdvancedSettings()
		}),
	)
}

// RefreshChat 重新整理聊天介面
func (ui *UI) RefreshChat() {
	if ui.chatHistory != nil {
		ui.chatHistory.Refresh()
		ui.chatHistory.ScrollToBottom()
	}
}

// Refresh 重新整理整個 UI
func (ui *UI) Refresh() {
	ui.RefreshChat()
	ui.updateModelList()
}

// sendMessage 發送訊息
func (ui *UI) sendMessage() {
	message, _ := ui.manager.currentMessage.Get()
	ui.manager.SendMessage(message)
	ui.manager.currentMessage.Set("")
}

// 快速動作方法
func (ui *UI) analyzeCode() {
	ui.manager.currentMessage.Set("請分析當前專案的程式碼結構和品質")
	ui.sendMessage()
}

func (ui *UI) projectAssist() {
	ui.manager.currentMessage.Set("請提供專案開發的建議和最佳實踐")
	ui.sendMessage()
}

func (ui *UI) debugAssist() {
	ui.manager.currentMessage.Set("請協助分析和解決程式錯誤")
	ui.sendMessage()
}

func (ui *UI) clearConversation() {
	ui.manager.ClearConversation()
}

// 設定相關方法
func (ui *UI) onProviderChanged(provider string) {
	ui.manager.config.DefaultProvider = provider
	ui.updateModelList()
}

func (ui *UI) onModelChanged(model string) {
	// TODO: 更新選定的模型
}

func (ui *UI) updateModelList() {
	if ui.modelSelect == nil {
		return
	}

	provider := ui.manager.providers[ui.manager.config.DefaultProvider]
	if provider != nil {
		models := provider.GetModels()
		ui.modelSelect.Options = models
		if len(models) > 0 {
			ui.modelSelect.SetSelected(models[0])
		}
	}
}

func (ui *UI) saveSettings() {
	// TODO: 實作設定儲存
}

func (ui *UI) resetSettings() {
	// TODO: 實作設定重設
}

func (ui *UI) refreshContext() {
	ui.createContextPanel()
}

func (ui *UI) saveConversation() {
	// TODO: 實作對話儲存
}

func (ui *UI) loadConversation() {
	// TODO: 實作對話載入
}

func (ui *UI) showAdvancedSettings() {
	// TODO: 顯示進階設定對話框
}
