// Package mcp internal/modules/mcp/ui.go
package mcp

import (
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
)

// UI MCP 模組的使用者介面
type UI struct {
	manager *Manager
	content fyne.CanvasObject

	// UI 元件
	serverList    *widget.List
	toolList      *widget.List
	resourceList  *widget.List
	messageLog    *widget.List
	serverDetails *fyne.Container

	// 資料綁定
	filterText  binding.String
	autoRefresh binding.Bool
}

// NewUI 建立新的 MCP UI
func NewUI(manager *Manager) *UI {
	ui := &UI{
		manager:     manager,
		filterText:  binding.NewString(),
		autoRefresh: binding.NewBool(),
	}
	ui.buildUI()
	return ui
}

// Content 返回 UI 內容
func (ui *UI) Content() fyne.CanvasObject {
	return ui.content
}

// buildUI 建立使用者介面
func (ui *UI) buildUI() {
	// 建立伺服器列表
	ui.createServerList()

	// 建立工具列表
	ui.createToolList()

	// 建立資源列表
	ui.createResourceList()

	// 建立訊息日誌
	ui.createMessageLog()

	// 建立伺服器詳細資訊
	ui.createServerDetails()

	// 建立控制面板
	controlPanel := ui.createControlPanel()

	// 左側面板 - 伺服器和詳細資訊
	leftPanel := container.NewVSplit(
		ui.createCard("MCP 伺服器", ui.serverList),
		ui.createCard("伺服器詳細資訊", ui.serverDetails),
	)
	leftPanel.SetOffset(0.4)

	// 中間面板 - 工具和資源
	middlePanel := container.NewVSplit(
		ui.createCard("可用工具", ui.toolList),
		ui.createCard("可用資源", ui.resourceList),
	)
	middlePanel.SetOffset(0.5)

	// 右側面板 - 訊息日誌
	rightPanel := ui.createCard("訊息日誌", ui.messageLog)

	// 主要內容區域 - 添加滾動支援
	mainContent := container.NewHSplit(
		container.NewScroll(leftPanel), // 左側面板添加滾動
		container.NewHSplit(container.NewScroll(middlePanel), container.NewScroll(rightPanel)), // 中間和右側面板添加滾動
	)

	// 組合最終佈局
	ui.content = container.NewBorder(
		controlPanel,
		nil,
		nil,
		nil,
		container.NewScroll(mainContent), // 主內容添加滾動
	)
}

// createServerList 建立伺服器列表
func (ui *UI) createServerList() {
	ui.serverList = widget.NewList(
		func() int {
			return len(ui.manager.GetServers())
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				container.NewHBox(
					widget.NewIcon(theme.ComputerIcon()),
					widget.NewLabelWithStyle("伺服器名稱", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
				),
				widget.NewLabel("狀態"),
				widget.NewLabel("版本"),
			)
		},
		func(id widget.ListItemID, item fyne.CanvasObject) {
			servers := ui.manager.GetServers()
			if id >= len(servers) {
				return
			}

			server := servers[id]
			container := item.(*fyne.Container)

			// 更新標題
			header := container.Objects[0].(*fyne.Container)
			icon := header.Objects[0].(*widget.Icon)
			nameLabel := header.Objects[1].(*widget.Label)

			// 根據狀態設定圖示
			switch server.Status {
			case "connected":
				icon.SetResource(theme.ConfirmIcon())
			case "disconnected":
				icon.SetResource(theme.CancelIcon())
			case "error":
				icon.SetResource(theme.ErrorIcon())
			}

			nameLabel.SetText(server.Name)

			// 更新狀態和版本
			container.Objects[1].(*widget.Label).SetText(fmt.Sprintf("狀態: %s", server.Status))
			container.Objects[2].(*widget.Label).SetText(fmt.Sprintf("版本: %s", server.Version))
		},
	)

	// 選擇伺服器時更新詳細資訊
	ui.serverList.OnSelected = func(id widget.ListItemID) {
		ui.manager.SelectServer(id)
		ui.updateToolList()
		ui.updateResourceList()
	}
}

// createToolList 建立工具列表
func (ui *UI) createToolList() {
	ui.toolList = widget.NewList(
		func() int {
			server := ui.manager.GetSelectedServer()
			if server == nil {
				return 0
			}
			return len(server.Tools)
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				widget.NewLabelWithStyle("工具名稱", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
				widget.NewLabel("描述"),
				widget.NewLabel("類別"),
			)
		},
		func(id widget.ListItemID, item fyne.CanvasObject) {
			server := ui.manager.GetSelectedServer()
			if server == nil || id >= len(server.Tools) {
				return
			}

			tool := server.Tools[id]
			container := item.(*fyne.Container)

			container.Objects[0].(*widget.Label).SetText(tool.Name)
			container.Objects[1].(*widget.Label).SetText(tool.Description)
			container.Objects[2].(*widget.Label).SetText(fmt.Sprintf("類別: %s", tool.Category))
		},
	)
}

// createResourceList 建立資源列表
func (ui *UI) createResourceList() {
	ui.resourceList = widget.NewList(
		func() int {
			server := ui.manager.GetSelectedServer()
			if server == nil {
				return 0
			}
			return len(server.Resources)
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				widget.NewLabelWithStyle("資源名稱", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
				widget.NewLabel("URI"),
				widget.NewLabel("類型"),
			)
		},
		func(id widget.ListItemID, item fyne.CanvasObject) {
			server := ui.manager.GetSelectedServer()
			if server == nil || id >= len(server.Resources) {
				return
			}

			resource := server.Resources[id]
			container := item.(*fyne.Container)

			container.Objects[0].(*widget.Label).SetText(resource.Name)
			container.Objects[1].(*widget.Label).SetText(resource.URI)
			container.Objects[2].(*widget.Label).SetText(resource.MimeType)
		},
	)
}

// createMessageLog 建立訊息日誌
func (ui *UI) createMessageLog() {
	ui.messageLog = widget.NewList(
		func() int {
			return len(ui.manager.GetMessages())
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				container.NewHBox(
					widget.NewIcon(theme.MailSendIcon()),
					widget.NewLabelWithStyle("方法", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
					widget.NewLabel("時間"),
				),
				widget.NewLabel("詳細資訊"),
			)
		},
		func(id widget.ListItemID, item fyne.CanvasObject) {
			messages := ui.manager.GetMessages()
			if id >= len(messages) {
				return
			}

			msg := messages[len(messages)-1-id] // 最新的在上面
			container := item.(*fyne.Container)

			// 更新標題
			header := container.Objects[0].(*fyne.Container)
			icon := header.Objects[0].(*widget.Icon)
			methodLabel := header.Objects[1].(*widget.Label)
			timeLabel := header.Objects[2].(*widget.Label)

			// 根據方向設定圖示
			if msg.Direction == "request" {
				icon.SetResource(theme.MailSendIcon())
			} else {
				icon.SetResource(theme.MailReplyIcon())
			}

			methodLabel.SetText(msg.Method)
			timeLabel.SetText(msg.Timestamp.Format("15:04:05"))

			// 更新詳細資訊
			detailLabel := container.Objects[1].(*widget.Label)
			if msg.Error != nil {
				detailLabel.SetText(fmt.Sprintf("錯誤: %s", msg.Error.Message))
			} else {
				detailLabel.SetText(fmt.Sprintf("ID: %s", msg.ID))
			}
		},
	)
}

// createServerDetails 建立伺服器詳細資訊
func (ui *UI) createServerDetails() {
	ui.serverDetails = container.NewVBox(
		widget.NewLabel("選擇一個伺服器查看詳細資訊"),
	)
}

// createControlPanel 建立控制面板
func (ui *UI) createControlPanel() *fyne.Container {
	// 重新整理按鈕
	refreshBtn := widget.NewButtonWithIcon("重新整理", theme.ViewRefreshIcon(), func() {
		ui.manager.refreshServers()
	})

	// 自動重新整理開關
	autoRefreshCheck := widget.NewCheckWithData("自動重新整理", ui.autoRefresh)

	// 過濾輸入
	filterEntry := widget.NewEntryWithData(ui.filterText)
	filterEntry.SetPlaceHolder("過濾伺服器...")

	// 連接新伺服器按鈕
	connectBtn := widget.NewButtonWithIcon("連接伺服器", theme.ContentAddIcon(), func() {
		ui.showConnectDialog()
	})

	return container.NewVBox(
		widget.NewLabelWithStyle("🔌 MCP PROTOCOL", fyne.TextAlignCenter, fyne.TextStyle{Bold: true, Monospace: true}),
		container.NewHBox(
			refreshBtn,
			autoRefreshCheck,
			widget.NewSeparator(),
			widget.NewLabel("過濾:"),
			filterEntry,
			widget.NewSeparator(),
			connectBtn,
		),
	)
}

// createCard 建立帶標題的卡片容器
func (ui *UI) createCard(title string, content fyne.CanvasObject) *fyne.Container {
	titleLabel := widget.NewLabelWithStyle(title, fyne.TextAlignLeading, fyne.TextStyle{Bold: true})
	return container.NewBorder(titleLabel, nil, nil, nil, content)
}

// UpdateServerDetails 更新伺服器詳細資訊
func (ui *UI) UpdateServerDetails() {
	server := ui.manager.GetSelectedServer()
	if server == nil {
		return
	}

	// 建立詳細資訊內容
	details := container.NewVBox(
		widget.NewLabelWithStyle(server.Name, fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		widget.NewSeparator(),
		widget.NewLabel(fmt.Sprintf("版本: %s", server.Version)),
		widget.NewLabel(fmt.Sprintf("狀態: %s", server.Status)),
		widget.NewLabel(fmt.Sprintf("端點: %s", server.Endpoint)),
		widget.NewLabel(fmt.Sprintf("最後連線: %s", server.LastSeen.Format("15:04:05"))),
		widget.NewSeparator(),
		widget.NewLabel("功能:"),
	)

	// 添加功能列表
	for _, cap := range server.Capabilities {
		details.Add(widget.NewLabel(fmt.Sprintf("• %s", cap)))
	}

	ui.serverDetails.Objects = details.Objects
	ui.serverDetails.Refresh()
}

// updateToolList 更新工具列表
func (ui *UI) updateToolList() {
	if ui.toolList != nil {
		ui.toolList.Refresh()
	}
}

// updateResourceList 更新資源列表
func (ui *UI) updateResourceList() {
	if ui.resourceList != nil {
		ui.resourceList.Refresh()
	}
}

// RefreshServers 重新整理伺服器列表
func (ui *UI) RefreshServers() {
	if ui.serverList != nil {
		ui.serverList.Refresh()
	}
	if ui.messageLog != nil {
		ui.messageLog.Refresh()
	}
}

// Refresh 重新整理整個 UI
func (ui *UI) Refresh() {
	ui.RefreshServers()
	ui.updateToolList()
	ui.updateResourceList()
}

// showConnectDialog 顯示連接對話框
func (ui *UI) showConnectDialog() {
	// TODO: 實作連接新伺服器的對話框
	ui.manager.logger.Info("Connect dialog requested")
}
